# AI Meme Generator

A fun web application that allows users to chat with an AI and generate memes based on the conversation. Built with Next.js, Tailwind CSS, OpenRouter API, and Imgflip API.

## Features

- Chat interface with AI assistant (powered by OpenRouter API)
- Meme generation based on conversation context (powered by Imgflip API)
- Local storage for chat history persistence
- Responsive design that works on desktop and mobile
- Fun, meme-like styling with animations and gradients

## Getting Started

### Prerequisites

- Node.js (version 14 or higher)
- npm or yarn

### Installation

1. Clone the repository:
   ```bash
   git clone <repository-url>
   ```

2. Install dependencies:
   ```bash
   npm install
   ```

3. Create a `.env.local` file in the root directory with the following environment variables:
   ```env
   NEXT_PUBLIC_OPENROUTER_API_KEY=your_openrouter_api_key
   NEXT_PUBLIC_IMGFLIP_USERNAME=your_imgflip_username
   NEXT_PUBLIC_IMGFLIP_PASSWORD=your_imgflip_password
   ```

4. Run the development server:
   ```bash
   npm run dev
   ```

5. Open [http://localhost:3000](http://localhost:3000) in your browser.

## How to Use

1. Start chatting with the AI in the chat interface
2. Ask the AI to create a meme for you (e.g., "Create a meme about coding")
3. The AI will respond and generate a meme based on your conversation
4. View your generated meme in the meme display section
5. Generate new memes using the "Generate New Meme" button

## Technologies Used

- [Next.js](https://nextjs.org/) - React framework
- [Tailwind CSS](https://tailwindcss.com/) - CSS framework
- [OpenRouter API](https://openrouter.ai/) - AI chat completions
- [Imgflip API](https://api.imgflip.com/) - Meme generation
- LocalStorage - For chat history persistence

## Project Structure

```
.
├── src/
│   ├── app/                 # Next.js app directory
│   │   ├── api/             # API routes
│   │   │   ├── chat/        # Chat API route
│   │   │   └── meme/        # Meme API route
│   │   ├── components/      # React components
│   │   └── lib/             # Utility functions
│   └── public/              # Static assets
├── .env.local              # Environment variables
└── README.md               # This file
```

## API Endpoints

### Chat API
- `POST /api/chat` - Send a message to the AI and get a response

### Meme API
- `GET /api/meme` - Get a list of available meme templates
- `POST /api/meme` - Generate a new meme

## Environment Variables

- `NEXT_PUBLIC_OPENROUTER_API_KEY` - Your OpenRouter API key
- `NEXT_PUBLIC_IMGFLIP_USERNAME` - Your Imgflip username
- `NEXT_PUBLIC_IMGFLIP_PASSWORD` - Your Imgflip password

## Learn More

To learn more about the technologies used in this project, take a look at the following resources:

- [Next.js Documentation](https://nextjs.org/docs)
- [Tailwind CSS Documentation](https://tailwindcss.com/docs)
- [OpenRouter API Documentation](https://openrouter.ai/docs)
- [Imgflip API Documentation](https://api.imgflip.com/)

## Deploy on Vercel

The easiest way to deploy your Next.js app is to use the [Vercel Platform](https://vercel.com/new?utm_medium=default-template&filter=next.js&utm_source=create-next-app&utm_campaign=create-next-app-readme) from the creators of Next.js.

Check out our [Next.js deployment documentation](https://nextjs.org/docs/deployment) for more details.
