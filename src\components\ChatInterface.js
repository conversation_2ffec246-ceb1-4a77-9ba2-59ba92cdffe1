import { useState, useRef, useEffect } from 'react';

export default function ChatInterface({ onMemeGenerated }) {
  const [messages, setMessages] = useState([]);
  const [inputValue, setInputValue] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const messagesEndRef = useRef(null);

  // Load chat history from localStorage on component mount
  useEffect(() => {
    const savedMessages = localStorage.getItem('memeGeneratorChat');
    if (savedMessages) {
      setMessages(JSON.parse(savedMessages));
    }
  }, []);

  // Save chat history to localStorage whenever messages change
  useEffect(() => {
    localStorage.setItem('memeGeneratorChat', JSON.stringify(messages));
  }, [messages]);

  // Scroll to bottom of chat
  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  }, [messages]);

  const handleSubmit = async (e) => {
    e.preventDefault();
    if (!inputValue.trim() || isLoading) return;

    // Add user message to chat
    const userMessage = { id: Date.now(), text: inputValue, sender: 'user' };
    setMessages(prev => [...prev, userMessage]);
    setInputValue('');
    setIsLoading(true);

    try {
      // Call OpenRouter API
      const response = await fetch('/api/chat', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ message: inputValue, history: messages }),
      });

      if (!response.ok) {
        throw new Error('Failed to get response from AI');
      }

      const data = await response.json();

      // Add AI response to chat
      const aiMessage = { id: Date.now() + 1, text: data.response, sender: 'ai' };
      setMessages(prev => [...prev, aiMessage]);

      // Check if AI generated a meme
      if (data.memeGenerated && data.memeUrl) {
        // Trigger meme display with the generated meme URL
        onMemeGenerated(data.memeUrl, true); // true indicates it's a direct meme URL
      }
    } catch (error) {
      console.error('Error:', error);
      const errorMessage = { id: Date.now() + 1, text: 'Sorry, something went wrong!', sender: 'ai' };
      setMessages(prev => [...prev, errorMessage]);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="flex flex-col h-[70vh] w-full">
      <div className="flex-1 overflow-y-auto p-4 bg-gray-50 rounded-2xl border border-gray-200">
        {messages.length === 0 ? (
          <div className="flex flex-col items-center justify-center h-full text-gray-500">
            <div className="text-6xl mb-6">🤖</div>
            <h3 className="text-2xl font-bold text-gray-700 mb-2">Ready to Create Memes!</h3>
            <p className="text-lg text-center mb-6">Ask me to create a meme and I'll generate it instantly using AI function calls</p>
            <div className="bg-gradient-to-r from-blue-50 to-purple-50 rounded-2xl p-6 max-w-md">
              <p className="font-bold text-gray-800 mb-3">✨ Try these prompts:</p>
              <div className="space-y-2 text-sm">
                <div className="bg-white rounded-lg p-3 shadow-sm">
                  <span className="text-blue-600 font-medium">"Create a meme about coding bugs"</span>
                </div>
                <div className="bg-white rounded-lg p-3 shadow-sm">
                  <span className="text-purple-600 font-medium">"Make a funny meme about Monday mornings"</span>
                </div>
                <div className="bg-white rounded-lg p-3 shadow-sm">
                  <span className="text-pink-600 font-medium">"Generate a meme about coffee addiction"</span>
                </div>
              </div>
            </div>
          </div>
        ) : (
          <div className="space-y-4">
            {messages.map((message) => (
              <div
                key={message.id}
                className={`flex ${message.sender === 'user' ? 'justify-end' : 'justify-start'}`}
              >
                <div
                  className={`max-w-xs md:max-w-md px-4 py-2 rounded-lg ${
                    message.sender === 'user'
                      ? 'bg-blue-500 text-white rounded-br-none'
                      : 'bg-gray-200 text-gray-800 rounded-bl-none'
                  }`}
                >
                  {message.text}
                </div>
              </div>
            ))}
            {isLoading && (
              <div className="flex justify-start">
                <div className="bg-gray-200 text-gray-800 px-4 py-2 rounded-lg rounded-bl-none">
                  <div className="flex space-x-1">
                    <div className="w-2 h-2 bg-gray-500 rounded-full animate-bounce"></div>
                    <div className="w-2 h-2 bg-gray-500 rounded-full animate-bounce" style={{ animationDelay: '0.2s' }}></div>
                    <div className="w-2 h-2 bg-gray-500 rounded-full animate-bounce" style={{ animationDelay: '0.4s' }}></div>
                  </div>
                </div>
              </div>
            )}
            <div ref={messagesEndRef} />
          </div>
        )}
      </div>
      
      <form onSubmit={handleSubmit} className="mt-4">
        <div className="flex gap-3">
          <input
            type="text"
            value={inputValue}
            onChange={(e) => setInputValue(e.target.value)}
            placeholder="Ask me to create a meme..."
            className="flex-1 border-2 border-gray-200 rounded-2xl px-6 py-4 text-lg focus:outline-none focus:border-blue-500 focus:ring-4 focus:ring-blue-100 transition-all"
            disabled={isLoading}
          />
          <button
            type="submit"
            className="bg-gradient-to-r from-blue-500 to-purple-600 text-white rounded-2xl px-8 py-4 font-semibold hover:from-blue-600 hover:to-purple-700 focus:outline-none focus:ring-4 focus:ring-blue-200 disabled:opacity-50 transition-all transform hover:scale-105 disabled:hover:scale-100"
            disabled={isLoading || !inputValue.trim()}
          >
            {isLoading ? (
              <div className="flex items-center gap-2">
                <div className="w-5 h-5 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
                <span>Thinking...</span>
              </div>
            ) : (
              <span className="flex items-center gap-2">
                <span>Send</span>
                <span className="text-xl">🚀</span>
              </span>
            )}
          </button>
        </div>
      </form>
    </div>
  );
}