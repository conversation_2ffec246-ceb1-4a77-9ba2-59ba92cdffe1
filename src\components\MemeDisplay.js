import { useState, useEffect, forwardRef, useImperativeHandle } from 'react';

const MemeDisplay = forwardRef(({ memeUrl, onNewMeme }, ref) => {
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState(null);
  const [memeTemplates, setMemeTemplates] = useState([]);
  const [conversation, setConversation] = useState([]);

  // Fetch meme templates when component mounts
  useEffect(() => {
    fetchMemeTemplates();
  }, []);

  const fetchMemeTemplates = async () => {
    try {
      const response = await fetch('/api/meme');
      const data = await response.json();
      
      if (!response.ok) {
        throw new Error(data.error || 'Failed to fetch meme templates');
      }
      
      setMemeTemplates(data.memes);
    } catch (err) {
      console.error('Error fetching meme templates:', err);
      setError(err.message);
    }
  };

  // Expose methods to parent component via ref
  useImperativeHandle(ref, () => ({
    updateConversation(newConversation) {
      setConversation(newConversation);
    },
    
    async generateNewMeme(conversationContext = null) {
      await generateMeme(conversationContext || conversation);
    }
  }));

  // Function to generate a new meme
  const generateMeme = async (conversationContext) => {
    setIsLoading(true);
    setError(null);
    
    try {
      // Use provided conversation context
      const context = conversationContext || conversation;
      
      // For demo purposes, we'll create a meme with context-aware text
      const memeResponse = await fetch('/api/meme', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          conversation: context
        }),
      });
      
      const memeData = await memeResponse.json();
      
      if (!memeResponse.ok) {
        throw new Error(memeData.error || 'Failed to create meme');
      }
      
      // Call the onNewMeme callback with the new meme URL
      onNewMeme(memeData.url);
    } catch (err) {
      console.error('Error generating meme:', err);
      setError(err.message);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="flex flex-col items-center justify-center p-4 bg-white rounded-lg shadow-md h-full">
      <h2 className="text-xl font-bold mb-4 text-center gradient-text">Your Meme Generator</h2>
      
      {error && (
        <div className="text-red-500 mb-4 text-center">
          Error: {error}
        </div>
      )}
      
      <div className="flex-1 flex items-center justify-center w-full">
        {memeUrl ? (
          <div className="relative max-w-full">
            <img 
              src={memeUrl} 
              alt="Generated meme" 
              className="max-w-full h-auto rounded-lg shadow-md meme-shadow animate-pulse-slow"
              onError={(e) => {
                e.target.src = 'https://via.placeholder.com/400x300.png?text=Failed+to+load+meme';
              }}
            />
          </div>
        ) : (
          <div className="flex flex-col items-center justify-center text-center p-4 bg-gray-100 rounded-lg w-full h-64">
            <div className="text-4xl mb-2">😂</div>
            <p className="text-gray-500">
              {isLoading ? 'Generating your meme...' : 'No meme generated yet'}
            </p>
            <p className="text-sm text-gray-400 mt-2">
              Chat with the AI to create a meme!
            </p>
          </div>
        )}
      </div>
      
      <button
        onClick={() => generateMeme()}
        disabled={isLoading}
        className={`mt-4 px-6 py-3 rounded-full font-bold text-white shadow-lg transform transition hover:scale-105 hover:wiggle ${
          isLoading 
            ? 'bg-gray-400 cursor-not-allowed' 
            : 'bg-gradient-to-r from-yellow-500 to-red-500 hover:from-yellow-600 hover:to-red-600'
        }`}
      >
        {isLoading ? (
          <span className="flex items-center">
            <span className="animate-spin mr-2">🌀</span> Generating...
          </span>
        ) : (
          <span className="flex items-center">
            <span className="mr-2">🎨</span> Generate New Meme
          </span>
        )}
      </button>
      
      <div className="mt-4 text-xs text-gray-500 text-center">
        <p>Powered by Imgflip API</p>
        <p className="mt-1">{memeTemplates.length} meme templates available</p>
      </div>
    </div>
  );
});

export default MemeDisplay;