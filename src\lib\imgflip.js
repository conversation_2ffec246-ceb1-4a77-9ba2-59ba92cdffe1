export async function getMemes() {
  try {
    const response = await fetch('https://api.imgflip.com/get_memes');
    const data = await response.json();
    
    if (!data.success) {
      throw new Error(data.error_message || 'Failed to fetch memes');
    }
    
    return data.data.memes;
  } catch (error) {
    console.error('Error fetching memes:', error);
    throw error;
  }
}

export async function createMeme(templateId, boxes) {
  try {
    const username = process.env.NEXT_PUBLIC_IMGFLIP_USERNAME;
    const password = process.env.NEXT_PUBLIC_IMGFLIP_PASSWORD;
    
    if (!username || !password) {
      throw new Error('Imgflip credentials not configured');
    }
    
    const formData = new FormData();
    formData.append('template_id', templateId);
    formData.append('username', username);
    formData.append('password', password);
    
    // Add text boxes
    boxes.forEach((box, index) => {
      Object.keys(box).forEach(key => {
        formData.append(`boxes[${index}][${key}]`, box[key]);
      });
    });
    
    const response = await fetch('https://api.imgflip.com/caption_image', {
      method: 'POST',
      body: formData,
    });
    
    const data = await response.json();
    
    if (!data.success) {
      throw new Error(data.error_message || 'Failed to create meme');
    }
    
    return data.data;
  } catch (error) {
    console.error('Error creating meme:', error);
    throw error;
  }
}

export async function createMemeWithText(templateId, text0, text1) {
  try {
    const username = process.env.NEXT_PUBLIC_IMGFLIP_USERNAME;
    const password = process.env.NEXT_PUBLIC_IMGFLIP_PASSWORD;
    
    if (!username || !password) {
      throw new Error('Imgflip credentials not configured');
    }
    
    const formData = new FormData();
    formData.append('template_id', templateId);
    formData.append('username', username);
    formData.append('password', password);
    formData.append('text0', text0);
    formData.append('text1', text1);
    
    const response = await fetch('https://api.imgflip.com/caption_image', {
      method: 'POST',
      body: formData,
    });
    
    const data = await response.json();
    
    if (!data.success) {
      throw new Error(data.error_message || 'Failed to create meme');
    }
    
    return data.data;
  } catch (error) {
    console.error('Error creating meme:', error);
    throw error;
  }
}

// Function to generate meme text based on conversation context
export function generateMemeText(conversation) {
  // This is a simple implementation - in a real app, you might use AI to generate better text
  const lastUserMessage = conversation.filter(msg => msg.sender === 'user').pop();
  const lastAiMessage = conversation.filter(msg => msg.sender === 'ai').pop();
  
  // Default meme text
  let topText = "When you finally";
  let bottomText = "deploy to production";
  
  if (lastUserMessage) {
    // Extract keywords from the last user message
    const userText = lastUserMessage.text.toLowerCase();
    
    if (userText.includes('monday') || userText.includes('week')) {
      topText = "When Monday morning";
      bottomText = "hits you like a truck";
    } else if (userText.includes('coffee')) {
      topText = "When you realize";
      bottomText = "you forgot your coffee";
    } else if (userText.includes('code') || userText.includes('programming')) {
      topText = "When your code";
      bottomText = "finally works on the first try";
    } else if (userText.includes('cat') || userText.includes('cats')) {
      topText = "When your cat";
      bottomText = "judges your code";
    } else {
      // Use the user's message as inspiration
      const words = lastUserMessage.text.split(' ');
      if (words.length > 1) {
        topText = words.slice(0, Math.min(3, Math.floor(words.length/2))).join(' ');
        bottomText = words.slice(Math.min(3, Math.floor(words.length/2))).join(' ');
      } else {
        topText = "When " + lastUserMessage.text;
        bottomText = "happens";
      }
    }
  }
  
  // Limit text length for better meme appearance
  topText = topText.substring(0, 30);
  bottomText = bottomText.substring(0, 30);
  
  return { topText, bottomText };
}