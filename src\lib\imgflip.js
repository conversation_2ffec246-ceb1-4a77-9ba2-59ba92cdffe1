export async function getMemes() {
  try {
    const response = await fetch('https://api.imgflip.com/get_memes');
    const data = await response.json();
    
    if (!data.success) {
      throw new Error(data.error_message || 'Failed to fetch memes');
    }
    
    return data.data.memes;
  } catch (error) {
    console.error('Error fetching memes:', error);
    throw error;
  }
}

export async function createMeme(templateId, boxes) {
  try {
    const username = process.env.NEXT_PUBLIC_IMGFLIP_USERNAME;
    const password = process.env.NEXT_PUBLIC_IMGFLIP_PASSWORD;
    
    if (!username || !password) {
      throw new Error('Imgflip credentials not configured');
    }
    
    const formData = new FormData();
    formData.append('template_id', templateId);
    formData.append('username', username);
    formData.append('password', password);
    
    // Add text boxes
    boxes.forEach((box, index) => {
      Object.keys(box).forEach(key => {
        formData.append(`boxes[${index}][${key}]`, box[key]);
      });
    });
    
    const response = await fetch('https://api.imgflip.com/caption_image', {
      method: 'POST',
      body: formData,
    });
    
    const data = await response.json();
    
    if (!data.success) {
      throw new Error(data.error_message || 'Failed to create meme');
    }
    
    return data.data;
  } catch (error) {
    console.error('Error creating meme:', error);
    throw error;
  }
}

export async function createMemeWithText(templateId, text0, text1) {
  try {
    const username = process.env.NEXT_PUBLIC_IMGFLIP_USERNAME;
    const password = process.env.NEXT_PUBLIC_IMGFLIP_PASSWORD;
    
    if (!username || !password) {
      throw new Error('Imgflip credentials not configured');
    }
    
    const formData = new FormData();
    formData.append('template_id', templateId);
    formData.append('username', username);
    formData.append('password', password);
    formData.append('text0', text0);
    formData.append('text1', text1);
    
    const response = await fetch('https://api.imgflip.com/caption_image', {
      method: 'POST',
      body: formData,
    });
    
    const data = await response.json();
    
    if (!data.success) {
      throw new Error(data.error_message || 'Failed to create meme');
    }
    
    return data.data;
  } catch (error) {
    console.error('Error creating meme:', error);
    throw error;
  }
}

// Function to generate meme text based on conversation context using AI
export async function generateMemeText(conversation) {
  try {
    // Get the OpenRouter API key from environment variables
    const apiKey = process.env.NEXT_PUBLIC_OPENROUTER_API_KEY;

    if (!apiKey) {
      throw new Error('OpenRouter API key not configured');
    }

    // Create a prompt to generate meme text based on conversation
    const conversationText = conversation
      .map(msg => `${msg.sender}: ${msg.text}`)
      .join('\n');

    const prompt = `Based on this conversation, generate funny and relevant meme text:

${conversationText}

Generate a meme with:
- Top text (max 30 characters): Should be the setup or context
- Bottom text (max 30 characters): Should be the punchline or conclusion

The meme should be funny, relatable, and directly related to the conversation topic. Use popular meme formats and internet humor.

Respond in this exact JSON format:
{
  "topText": "your top text here",
  "bottomText": "your bottom text here"
}`;

    // Call the OpenRouter API
    const response = await fetch('https://openrouter.ai/api/v1/chat/completions', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${apiKey}`,
        'Content-Type': 'application/json',
        'HTTP-Referer': 'http://localhost:3000',
        'X-Title': 'AI Meme Generator'
      },
      body: JSON.stringify({
        model: 'google/gemini-2.5-flash',
        messages: [
          { role: 'user', content: prompt }
        ],
        temperature: 0.8,
      }),
    });

    if (!response.ok) {
      const errorData = await response.json();
      console.error('OpenRouter API error:', errorData);
      throw new Error(`OpenRouter API error: ${errorData.error?.message || 'Unknown error'}`);
    }

    const data = await response.json();
    const aiResponse = data.choices[0].message.content;

    // Try to parse the JSON response
    try {
      const memeData = JSON.parse(aiResponse);

      // Validate and clean the response
      let topText = memeData.topText || "When you finally";
      let bottomText = memeData.bottomText || "get it right";

      // Ensure text length limits
      topText = topText.substring(0, 30);
      bottomText = bottomText.substring(0, 30);

      return { topText, bottomText };
    } catch (parseError) {
      console.error('Failed to parse AI response as JSON:', aiResponse);

      // Fallback: try to extract text manually
      const lines = aiResponse.split('\n').filter(line => line.trim());
      let topText = "When you finally";
      let bottomText = "get it right";

      // Look for patterns like "Top text:" or "Bottom text:"
      for (const line of lines) {
        if (line.toLowerCase().includes('top') && line.includes(':')) {
          topText = line.split(':')[1].trim().replace(/['"]/g, '').substring(0, 30);
        } else if (line.toLowerCase().includes('bottom') && line.includes(':')) {
          bottomText = line.split(':')[1].trim().replace(/['"]/g, '').substring(0, 30);
        }
      }

      return { topText, bottomText };
    }
  } catch (error) {
    console.error('Error generating AI meme text, falling back to simple generation:', error);

    // Fallback to simple text generation
    const lastUserMessage = conversation.filter(msg => msg.sender === 'user').pop();

    // Default meme text
    let topText = "When you finally";
    let bottomText = "deploy to production";

    if (lastUserMessage) {
      // Extract keywords from the last user message
      const userText = lastUserMessage.text.toLowerCase();

      if (userText.includes('monday') || userText.includes('week')) {
        topText = "When Monday morning";
        bottomText = "hits you like a truck";
      } else if (userText.includes('coffee')) {
        topText = "When you realize";
        bottomText = "you forgot your coffee";
      } else if (userText.includes('code') || userText.includes('programming')) {
        topText = "When your code";
        bottomText = "finally works on the first try";
      } else if (userText.includes('cat') || userText.includes('cats')) {
        topText = "When your cat";
        bottomText = "judges your code";
      } else {
        // Use the user's message as inspiration
        const words = lastUserMessage.text.split(' ');
        if (words.length > 1) {
          topText = words.slice(0, Math.min(3, Math.floor(words.length/2))).join(' ');
          bottomText = words.slice(Math.min(3, Math.floor(words.length/2))).join(' ');
        } else {
          topText = "When " + lastUserMessage.text;
          bottomText = "happens";
        }
      }
    }

    // Limit text length for better meme appearance
    topText = topText.substring(0, 30);
    bottomText = bottomText.substring(0, 30);

    return { topText, bottomText };
  }
}