"use client";

import ChatInterface from '@/components/ChatInterface';
import MemeDisplay from '@/components/MemeDisplay';
import { useState, useRef } from 'react';

export default function Home() {
  const [memeUrl, setMemeUrl] = useState(null);
  const memeDisplayRef = useRef();

  const handleNewMeme = (url) => {
    setMemeUrl(url);
  };

  const handleMemeGenerated = (memeUrlOrConversation, isDirectUrl = false) => {
    if (isDirectUrl) {
      // AI directly generated a meme, just display it
      setMemeUrl(memeUrlOrConversation);
    } else {
      // This function will be called when the AI suggests creating a meme
      // We'll generate a meme based on the conversation context
      if (memeDisplayRef.current) {
        memeDisplayRef.current.updateConversation(memeUrlOrConversation);
        memeDisplayRef.current.generateNewMeme(memeUrlOrConversation);
      }
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 to-slate-100 p-4">
      <div className="max-w-7xl mx-auto">
        <header className="text-center py-8 mb-8">
          <h1 className="text-5xl md:text-6xl font-bold bg-clip-text text-transparent bg-gradient-to-r from-blue-600 via-purple-600 to-pink-600 mb-4">
            AI Meme Generator
          </h1>
          <p className="text-xl text-gray-600 max-w-2xl mx-auto">
            Chat with AI and watch it create hilarious memes instantly using function calls!
          </p>
        </header>

        <div className="grid grid-cols-1 xl:grid-cols-2 gap-8">
          {/* Chat Section */}
          <div className="bg-white rounded-3xl shadow-2xl overflow-hidden border border-gray-200">
            <div className="bg-gradient-to-r from-blue-500 to-purple-600 p-6">
              <h2 className="text-2xl font-bold text-white flex items-center gap-3">
                <span className="text-3xl">💬</span>
                Chat with AI
              </h2>
              <p className="text-blue-100 mt-2">Ask me to create a meme and I'll generate it instantly!</p>
            </div>
            <div className="p-6">
              <ChatInterface onMemeGenerated={handleMemeGenerated} />
            </div>
          </div>

          {/* Meme Display Section */}
          <div className="bg-white rounded-3xl shadow-2xl overflow-hidden border border-gray-200">
            <div className="bg-gradient-to-r from-purple-500 to-pink-600 p-6">
              <h2 className="text-2xl font-bold text-white flex items-center gap-3">
                <span className="text-3xl">🎭</span>
                Generated Memes
              </h2>
              <p className="text-purple-100 mt-2">Your AI-generated memes will appear here</p>
            </div>
            <div className="p-6">
              <MemeDisplay ref={memeDisplayRef} memeUrl={memeUrl} onNewMeme={handleNewMeme} />
            </div>
          </div>
        </div>

        <footer className="text-center py-8 mt-12">
          <div className="bg-white rounded-2xl shadow-lg p-6 max-w-2xl mx-auto">
            <p className="text-lg font-semibold text-gray-800 mb-2">
              🚀 Powered by AI Function Calls
            </p>
            <p className="text-gray-600">
              This app uses OpenRouter API with function calling to generate contextual memes instantly!
            </p>
          </div>
        </footer>
      </div>
    </div>
  );
}
