import ChatInterface from '@/components/ChatInterface';
import MemeDisplay from '@/components/MemeDisplay';
import { useState, useRef } from 'react';

export default function Home() {
  const [memeUrl, setMemeUrl] = useState(null);
  const memeDisplayRef = useRef();

  const handleNewMeme = (url) => {
    setMemeUrl(url);
  };

  const handleMemeGenerated = (conversation) => {
    // This function will be called when the AI suggests creating a meme
    // We'll generate a meme based on the conversation context
    if (memeDisplayRef.current) {
      memeDisplayRef.current.updateConversation(conversation);
      memeDisplayRef.current.generateNewMeme(conversation);
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-yellow-100 via-pink-100 to-purple-100 p-4 md:p-8">
      <div className="max-w-6xl mx-auto">
        <header className="text-center py-6 mb-8">
          <h1 className="text-4xl md:text-6xl font-extrabold bg-clip-text text-transparent bg-gradient-to-r from-purple-600 to-pink-500 animate-pulse">
            AI Meme Generator
          </h1>
          <p className="text-lg md:text-xl text-gray-700 mt-2">
            Chat with AI and create hilarious memes instantly!
          </p>
        </header>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 md:gap-8">
          <div className="bg-white rounded-2xl shadow-xl overflow-hidden border-4 border-dashed border-purple-300">
            <div className="bg-gradient-to-r from-purple-500 to-pink-500 p-4">
              <h2 className="text-2xl font-bold text-white text-center">Chat with AI</h2>
            </div>
            <div className="p-4">
              <ChatInterface onMemeGenerated={handleMemeGenerated} />
            </div>
          </div>

          <div className="bg-white rounded-2xl shadow-xl overflow-hidden border-4 border-dashed border-yellow-300">
            <div className="bg-gradient-to-r from-yellow-500 to-red-500 p-4">
              <h2 className="text-2xl font-bold text-white text-center">Meme Generator</h2>
            </div>
            <div className="p-4 h-full">
              <MemeDisplay ref={memeDisplayRef} memeUrl={memeUrl} onNewMeme={handleNewMeme} />
            </div>
          </div>
        </div>

        <footer className="text-center py-8 mt-8 text-gray-600">
          <p className="text-lg">
            Made with ❤️ and 🍕 | AI Meme Generator © {new Date().getFullYear()}
          </p>
          <p className="mt-2 text-sm">
            Chat with our AI to create the funniest memes on the internet!
          </p>
        </footer>
      </div>
    </div>
  );
}
