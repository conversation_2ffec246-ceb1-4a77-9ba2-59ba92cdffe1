import { NextResponse } from 'next/server';

export async function POST(request) {
  try {
    const { message, history } = await request.json();
    
    // Get the OpenRouter API key from environment variables
    const apiKey = process.env.NEXT_PUBLIC_OPENROUTER_API_KEY;
    
    if (!apiKey) {
      return NextResponse.json(
        { error: 'OpenRouter API key not configured' },
        { status: 500 }
      );
    }
    
    // Format the chat history for the OpenRouter API
    const formattedHistory = history.map(msg => ({
      role: msg.sender === 'user' ? 'user' : 'assistant',
      content: msg.text
    }));
    
    // Add the current user message
    const messages = [
      ...formattedHistory,
      { role: 'user', content: message }
    ];
    
    // Call the OpenRouter API
    const response = await fetch('https://openrouter.ai/api/v1/chat/completions', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${apiKey}`,
        'Content-Type': 'application/json',
        'HTTP-Referer': 'http://localhost:3000', // Optional, for including your app on openrouter.ai rankings
        'X-Title': 'AI Meme Generator' // Optional, for including your app on openrouter.ai rankings
      },
      body: JSON.stringify({
        model: 'google/gemini-2.5-flash',
        messages: messages,
        temperature: 0.7,
        tools: [
          {
            type: "function",
            function: {
              name: "generate_meme",
              description: "Generate a meme based on the conversation context. Use this when the user asks to create, make, or generate a meme.",
              parameters: {
                type: "object",
                properties: {
                  topText: {
                    type: "string",
                    description: "The top text for the meme (max 30 characters). Should be the setup or context."
                  },
                  bottomText: {
                    type: "string",
                    description: "The bottom text for the meme (max 30 characters). Should be the punchline or conclusion."
                  },
                  context: {
                    type: "string",
                    description: "Brief description of what the meme is about based on the conversation."
                  }
                },
                required: ["topText", "bottomText", "context"]
              }
            }
          }
        ],
        tool_choice: "auto"
      }),
    });
    
    if (!response.ok) {
      const errorData = await response.json();
      console.error('OpenRouter API error:', errorData);
      return NextResponse.json(
        { error: `OpenRouter API error: ${errorData.error?.message || 'Unknown error'}` },
        { status: response.status }
      );
    }
    
    const data = await response.json();
    const aiMessage = data.choices[0].message;

    // Check if AI wants to call a function
    if (aiMessage.tool_calls && aiMessage.tool_calls.length > 0) {
      const toolCall = aiMessage.tool_calls[0];

      if (toolCall.function.name === 'generate_meme') {
        const functionArgs = JSON.parse(toolCall.function.arguments);

        // Generate the meme using the AI-provided text
        try {
          const memeResponse = await fetch('http://localhost:3000/api/meme', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify({
              text0: functionArgs.topText,
              text1: functionArgs.bottomText
            }),
          });

          const memeData = await memeResponse.json();

          if (memeResponse.ok) {
            return NextResponse.json({
              response: `I've created a meme for you! ${functionArgs.context}`,
              memeUrl: memeData.url,
              memeGenerated: true
            });
          } else {
            return NextResponse.json({
              response: `I wanted to create a meme about "${functionArgs.context}" with the text "${functionArgs.topText}" and "${functionArgs.bottomText}", but there was an error generating it. Let me try a different approach!`,
              memeGenerated: false
            });
          }
        } catch (error) {
          console.error('Error generating meme from function call:', error);
          return NextResponse.json({
            response: `I wanted to create a meme about "${functionArgs.context}", but there was an error. Let me help you in another way!`,
            memeGenerated: false
          });
        }
      }
    }

    // Regular text response
    const aiResponse = aiMessage.content;
    return NextResponse.json({ response: aiResponse });
  } catch (error) {
    console.error('Error in chat API:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}