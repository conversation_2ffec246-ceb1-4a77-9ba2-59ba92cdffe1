import { NextResponse } from 'next/server';
import { getMemes, createMeme, createMemeWithText, generateMemeText } from '@/lib/imgflip';

export async function GET() {
  try {
    const memes = await getMemes();
    return NextResponse.json({ memes });
  } catch (error) {
    console.error('Error fetching memes:', error);
    return NextResponse.json(
      { error: 'Failed to fetch memes' },
      { status: 500 }
    );
  }
}

export async function POST(request) {
  try {
    const { templateId, text0, text1, boxes, conversation } = await request.json();
    
    let result;
    if (boxes) {
      result = await createMeme(templateId, boxes);
    } else if (text0 !== undefined && text1 !== undefined) {
      result = await createMemeWithText(templateId, text0, text1);
    } else {
      // Generate meme text based on conversation if provided
      const { topText, bottomText } = conversation 
        ? generateMemeText(conversation)
        : { topText: "When you finally", bottomText: "deploy to production" };
      
      // Get memes and select a random one if templateId not provided
      const memes = await getMemes();
      const selectedTemplateId = templateId || memes[Math.floor(Math.random() * memes.length)].id;
      
      result = await createMemeWithText(selectedTemplateId, topText, bottomText);
    }
    
    return NextResponse.json(result);
  } catch (error) {
    console.error('Error creating meme:', error);
    return NextResponse.json(
      { error: error.message || 'Failed to create meme' },
      { status: 500 }
    );
  }
}